"""
增强可视化API路由
"""

from fastapi import APIRouter, Depends, HTTPException, Query
from sqlalchemy.orm import Session
from typing import Dict, List, Optional, Any
import logging

from app.db.init_db import get_db
from app.models.draw import Draw
from app.services.enhanced_visualization_service import (
    EnhancedVisualizationService,
    calculate_correlation_matrix,
    export_visualization_data
)
from app.services.statistics_service import get_number_frequency

logger = logging.getLogger(__name__)

router = APIRouter(prefix="/api/enhanced-visualization", tags=["enhanced-visualization"])

# 初始化可视化服务
visualization_service = EnhancedVisualizationService()


@router.get("/heatmap/{heatmap_type}")
async def get_heatmap(
    heatmap_type: str = Query(..., description="热力图类型: frequency, missing, heat"),
    year: Optional[int] = Query(None, description="年份筛选"),
    db: Session = Depends(get_db)
):
    """获取热力图数据"""
    try:
        # 获取统计数据
        statistics_data = get_number_frequency(db)
        
        # 生成热力图
        heatmap_result = visualization_service.heatmap_generator.generate_number_heatmap(
            statistics_data, heatmap_type
        )
        
        return {
            "success": True,
            "data": heatmap_result,
            "message": f"成功生成{heatmap_type}热力图"
        }
        
    except Exception as e:
        logger.error(f"获取热力图失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"获取热力图失败: {str(e)}")


@router.get("/timeline/{number}")
async def get_number_timeline(
    number: int = Query(..., description="号码", ge=1, le=49),
    chart_type: str = Query("scatter", description="图表类型: scatter, line, bar"),
    db: Session = Depends(get_db)
):
    """获取号码时间序列图"""
    try:
        # 获取历史数据
        draws = db.query(Draw).order_by(Draw.draw_time.desc()).limit(500).all()
        historical_data = []
        
        for draw in draws:
            if draw.numbers:
                historical_data.append({
                    'draw_time': draw.draw_time.strftime('%Y-%m-%d') if draw.draw_time else '',
                    'expect': draw.expect,
                    'numbers': draw.numbers
                })
        
        # 生成时间序列图
        timeline_result = visualization_service.timeline_visualizer.generate_number_timeline(
            number, historical_data, chart_type
        )
        
        return {
            "success": True,
            "data": timeline_result,
            "message": f"成功生成号码{number}的时间序列图"
        }
        
    except Exception as e:
        logger.error(f"获取时间序列图失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"获取时间序列图失败: {str(e)}")


@router.get("/frequency-trend")
async def get_frequency_trend(
    time_window: str = Query("month", description="时间窗口: month, quarter, year"),
    db: Session = Depends(get_db)
):
    """获取频率趋势图"""
    try:
        # 获取历史数据
        draws = db.query(Draw).order_by(Draw.draw_time.desc()).limit(1000).all()
        historical_data = []
        
        for draw in draws:
            if draw.numbers:
                historical_data.append({
                    'draw_time': draw.draw_time.strftime('%Y-%m-%d') if draw.draw_time else '',
                    'expect': draw.expect,
                    'numbers': draw.numbers
                })
        
        # 生成频率趋势图
        trend_result = visualization_service.timeline_visualizer.generate_frequency_trend(
            historical_data, time_window
        )
        
        return {
            "success": True,
            "data": trend_result,
            "message": f"成功生成{time_window}频率趋势图"
        }
        
    except Exception as e:
        logger.error(f"获取频率趋势图失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"获取频率趋势图失败: {str(e)}")


@router.get("/network/correlation")
async def get_correlation_network(
    threshold: float = Query(0.3, description="关联阈值", ge=0.1, le=1.0),
    db: Session = Depends(get_db)
):
    """获取号码关联网络图"""
    try:
        # 获取历史数据和统计数据
        draws = db.query(Draw).order_by(Draw.draw_time.desc()).limit(500).all()
        historical_data = []
        
        for draw in draws:
            if draw.numbers:
                historical_data.append({
                    'numbers': draw.numbers
                })
        
        statistics_data = get_number_frequency(db)
        
        # 计算关联矩阵
        correlation_matrix = calculate_correlation_matrix(historical_data)
        
        # 生成关联网络图
        network_result = visualization_service.network_generator.generate_correlation_network(
            correlation_matrix, statistics_data, threshold
        )
        
        return {
            "success": True,
            "data": network_result,
            "message": "成功生成号码关联网络图"
        }
        
    except Exception as e:
        logger.error(f"获取关联网络图失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"获取关联网络图失败: {str(e)}")


@router.get("/network/zodiac")
async def get_zodiac_network(db: Session = Depends(get_db)):
    """获取生肖关联网络图"""
    try:
        # 获取历史数据
        draws = db.query(Draw).order_by(Draw.draw_time.desc()).limit(500).all()
        historical_data = []
        
        for draw in draws:
            if draw.numbers:
                historical_data.append({
                    'numbers': draw.numbers
                })
        
        # 生成生肖网络图
        network_result = visualization_service.network_generator.generate_zodiac_network(
            historical_data
        )
        
        return {
            "success": True,
            "data": network_result,
            "message": "成功生成生肖关联网络图"
        }
        
    except Exception as e:
        logger.error(f"获取生肖网络图失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"获取生肖网络图失败: {str(e)}")


@router.get("/filters/config")
async def get_filter_config():
    """获取筛选控件配置"""
    try:
        filter_config = visualization_service.filter_controller.create_filter_controls()
        
        return {
            "success": True,
            "data": filter_config,
            "message": "成功获取筛选控件配置"
        }
        
    except Exception as e:
        logger.error(f"获取筛选配置失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"获取筛选配置失败: {str(e)}")


@router.post("/filters/apply")
async def apply_filters(
    filters: Dict[str, Any],
    db: Session = Depends(get_db)
):
    """应用筛选条件"""
    try:
        # 获取统计数据
        statistics_data = get_number_frequency(db)
        
        # 转换为列表格式
        data_list = []
        for number_str, stats in statistics_data.items():
            data_list.append({
                'number': int(number_str),
                'frequency': stats.get('count', 0),
                'missing_periods': stats.get('missing_periods', 0),
                'heat_index': stats.get('heat_index', 0),
                # 添加其他需要的字段
                'zodiac': _get_zodiac(int(number_str)),
                'color': _get_color(int(number_str)),
                'heat_level': _get_heat_level(stats.get('count', 0))
            })
        
        # 应用筛选
        filter_result = visualization_service.apply_interactive_filters(data_list, filters)
        
        return {
            "success": True,
            "data": filter_result,
            "message": "成功应用筛选条件"
        }
        
    except Exception as e:
        logger.error(f"应用筛选失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"应用筛选失败: {str(e)}")


@router.get("/comprehensive")
async def get_comprehensive_visualization(
    include_correlation: bool = Query(True, description="是否包含关联分析"),
    db: Session = Depends(get_db)
):
    """获取综合可视化分析"""
    try:
        # 获取数据
        draws = db.query(Draw).order_by(Draw.draw_time.desc()).limit(500).all()
        historical_data = []
        
        for draw in draws:
            if draw.numbers:
                historical_data.append({
                    'draw_time': draw.draw_time.strftime('%Y-%m-%d') if draw.draw_time else '',
                    'expect': draw.expect,
                    'numbers': draw.numbers
                })
        
        statistics_data = get_number_frequency(db)
        
        # 计算关联矩阵（如果需要）
        correlation_matrix = None
        if include_correlation:
            correlation_matrix = calculate_correlation_matrix(historical_data)
        
        # 生成综合可视化
        comprehensive_result = visualization_service.generate_comprehensive_visualization(
            statistics_data, historical_data, correlation_matrix
        )
        
        return {
            "success": True,
            "data": comprehensive_result,
            "message": "成功生成综合可视化分析"
        }
        
    except Exception as e:
        logger.error(f"获取综合可视化失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"获取综合可视化失败: {str(e)}")


@router.get("/number/{number}/analysis")
async def get_number_analysis(
    number: int = Query(..., description="号码", ge=1, le=49),
    db: Session = Depends(get_db)
):
    """获取特定号码的可视化分析"""
    try:
        # 获取数据
        draws = db.query(Draw).order_by(Draw.draw_time.desc()).limit(500).all()
        historical_data = []
        
        for draw in draws:
            if draw.numbers:
                historical_data.append({
                    'draw_time': draw.draw_time.strftime('%Y-%m-%d') if draw.draw_time else '',
                    'expect': draw.expect,
                    'numbers': draw.numbers
                })
        
        statistics_data = get_number_frequency(db)
        
        # 生成号码分析
        number_result = visualization_service.generate_number_specific_visualization(
            number, statistics_data, historical_data
        )
        
        return {
            "success": True,
            "data": number_result,
            "message": f"成功生成号码{number}的可视化分析"
        }
        
    except Exception as e:
        logger.error(f"获取号码分析失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"获取号码分析失败: {str(e)}")


@router.get("/export")
async def export_visualization(
    format_type: str = Query("json", description="导出格式: json, csv"),
    visualization_type: str = Query("comprehensive", description="可视化类型"),
    db: Session = Depends(get_db)
):
    """导出可视化数据"""
    try:
        # 根据类型获取数据
        if visualization_type == "comprehensive":
            # 获取综合数据
            draws = db.query(Draw).order_by(Draw.draw_time.desc()).limit(500).all()
            historical_data = []
            
            for draw in draws:
                if draw.numbers:
                    historical_data.append({
                        'draw_time': draw.draw_time.strftime('%Y-%m-%d') if draw.draw_time else '',
                        'expect': draw.expect,
                        'numbers': draw.numbers
                    })
            
            statistics_data = get_number_frequency(db)
            correlation_matrix = calculate_correlation_matrix(historical_data)
            
            visualization_result = visualization_service.generate_comprehensive_visualization(
                statistics_data, historical_data, correlation_matrix
            )
        else:
            visualization_result = {"message": "暂不支持该类型的导出"}
        
        # 导出数据
        exported_data = export_visualization_data(visualization_result, format_type)
        
        return {
            "success": True,
            "data": exported_data,
            "format": format_type,
            "message": f"成功导出{format_type}格式的可视化数据"
        }
        
    except Exception as e:
        logger.error(f"导出可视化数据失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"导出可视化数据失败: {str(e)}")


# 辅助函数
def _get_zodiac(number: int) -> str:
    """获取生肖"""
    zodiac_map = {
        1: '鼠', 2: '牛', 3: '虎', 4: '兔', 5: '龙', 6: '蛇',
        7: '马', 8: '羊', 9: '猴', 10: '鸡', 11: '狗', 0: '猪'
    }
    return zodiac_map.get(number % 12, '未知')


def _get_color(number: int) -> str:
    """获取波色"""
    red_numbers = [1, 2, 7, 8, 12, 13, 18, 19, 23, 24, 29, 30, 34, 35, 40, 45, 46]
    blue_numbers = [3, 4, 9, 10, 14, 15, 20, 25, 26, 31, 36, 37, 41, 42, 47, 48]
    
    if number in red_numbers:
        return '红波'
    elif number in blue_numbers:
        return '蓝波'
    else:
        return '绿波'


def _get_heat_level(frequency: int) -> str:
    """获取热度等级"""
    if frequency > 20:
        return '极热'
    elif frequency > 15:
        return '很热'
    elif frequency > 10:
        return '热门'
    elif frequency > 5:
        return '温和'
    else:
        return '冷门'
