"""
增强分析服务 - 实现概率预测模型完善、可视化增强和缺失分析维度补充
"""

import numpy as np
import pandas as pd
from typing import Dict, List, Tuple, Any, Optional
from datetime import datetime, timedelta
from scipy import stats
from scipy.stats import beta as beta_dist
from sklearn.cluster import KMeans, DBSCAN, AgglomerativeClustering
from sklearn.preprocessing import StandardScaler
from sklearn.linear_model import LinearRegression, Ridge, Lasso
from sklearn.preprocessing import PolynomialFeatures
import logging

logger = logging.getLogger(__name__)


class BayesianPredictionModel:
    """贝叶斯概率预测模型"""
    
    def __init__(self):
        self.prior_probabilities = {}
        self.conditional_probabilities = {}
        self.posterior_probabilities = {}
        self.historical_data = []
    
    def update_historical_data(self, data: List[int]):
        """更新历史数据"""
        self.historical_data = data
        self._calculate_prior_probabilities()
    
    def _calculate_prior_probabilities(self):
        """计算先验概率"""
        if not self.historical_data:
            return
        
        total_count = len(self.historical_data)
        for number in range(1, 50):
            count = self.historical_data.count(number)
            self.prior_probabilities[number] = count / total_count
    
    def calculate_number_probability(self, number: int) -> Dict[str, float]:
        """计算每个号码的开出概率"""
        if not self.historical_data:
            return {'probability': 1/49, 'confidence_interval': (0.01, 0.03)}
        
        # 基于历史频率的先验概率
        prior = self.prior_probabilities.get(number, 1/49)
        
        # 基于遗漏期数的条件概率
        missing_periods = self._get_missing_periods(number)
        likelihood = self._calculate_likelihood(missing_periods)
        
        # 贝叶斯后验概率
        evidence = sum(self.prior_probabilities.values())
        posterior = (likelihood * prior) / evidence if evidence > 0 else prior
        
        # 计算置信区间
        confidence_interval = self.get_confidence_interval(posterior)
        
        return {
            'probability': posterior,
            'confidence_interval': confidence_interval,
            'prior': prior,
            'likelihood': likelihood
        }
    
    def _get_missing_periods(self, number: int) -> int:
        """获取号码的遗漏期数"""
        if not self.historical_data:
            return 0
        
        for i in range(len(self.historical_data) - 1, -1, -1):
            if self.historical_data[i] == number:
                return len(self.historical_data) - 1 - i
        
        return len(self.historical_data)
    
    def _calculate_likelihood(self, missing_periods: int) -> float:
        """基于遗漏期数计算似然概率"""
        # 使用指数衰减模型：遗漏期数越长，开出概率越高
        if missing_periods == 0:
            return 0.5  # 刚开出的号码概率较低
        elif missing_periods <= 5:
            return 0.6
        elif missing_periods <= 10:
            return 0.8
        elif missing_periods <= 20:
            return 0.9
        else:
            return 0.95  # 长期遗漏的号码概率很高
    
    def get_confidence_interval(self, probability: float, confidence_level: float = 0.95) -> Tuple[float, float]:
        """计算置信区间"""
        # 使用Beta分布计算置信区间
        n = len(self.historical_data) if self.historical_data else 100
        successes = max(1, int(probability * n))
        
        alpha = successes
        beta = n - successes
        
        lower = beta_dist.ppf((1 - confidence_level) / 2, alpha, beta)
        upper = beta_dist.ppf(1 - (1 - confidence_level) / 2, alpha, beta)
        
        return (max(0, lower), min(1, upper))


class MarkovChainAnalyzer:
    """马尔可夫链分析器"""
    
    def __init__(self, order: int = 2):
        self.order = order
        self.transition_matrix = {}
        self.state_frequencies = {}
    
    def build_transition_matrix(self, historical_data: List[int]):
        """构建转移概率矩阵"""
        if len(historical_data) < self.order + 1:
            logger.warning(f"数据长度不足，需要至少 {self.order + 1} 个数据点")
            return
        
        # 统计状态转移
        for i in range(len(historical_data) - self.order):
            current_state = tuple(historical_data[i:i+self.order])
            next_state = historical_data[i+self.order]
            
            if current_state not in self.transition_matrix:
                self.transition_matrix[current_state] = {}
            
            if next_state not in self.transition_matrix[current_state]:
                self.transition_matrix[current_state][next_state] = 0
            
            self.transition_matrix[current_state][next_state] += 1
        
        # 归一化为概率
        self._normalize_probabilities()
        
        # 计算状态频率
        self._calculate_state_frequencies(historical_data)
    
    def _normalize_probabilities(self):
        """归一化转移概率"""
        for current_state in self.transition_matrix:
            total = sum(self.transition_matrix[current_state].values())
            if total > 0:
                for next_state in self.transition_matrix[current_state]:
                    self.transition_matrix[current_state][next_state] /= total
    
    def _calculate_state_frequencies(self, historical_data: List[int]):
        """计算状态频率"""
        for i in range(len(historical_data) - self.order + 1):
            state = tuple(historical_data[i:i+self.order])
            self.state_frequencies[state] = self.state_frequencies.get(state, 0) + 1
    
    def predict_next_number(self, recent_numbers: List[int]) -> Dict[int, float]:
        """预测下一个号码的概率分布"""
        if len(recent_numbers) < self.order:
            # 如果数据不足，返回均匀分布
            return {i: 1/49 for i in range(1, 50)}
        
        current_state = tuple(recent_numbers[-self.order:])
        
        if current_state in self.transition_matrix:
            return self.transition_matrix[current_state]
        else:
            # 如果状态未见过，尝试降阶匹配
            return self._fallback_prediction(recent_numbers)
    
    def _fallback_prediction(self, recent_numbers: List[int]) -> Dict[int, float]:
        """降阶预测作为后备方案"""
        for order in range(self.order - 1, 0, -1):
            if len(recent_numbers) >= order:
                state = tuple(recent_numbers[-order:])
                # 查找包含该子状态的所有状态
                matching_transitions = {}
                for full_state, transitions in self.transition_matrix.items():
                    if full_state[-order:] == state:
                        for next_num, prob in transitions.items():
                            matching_transitions[next_num] = matching_transitions.get(next_num, 0) + prob
                
                if matching_transitions:
                    # 归一化
                    total = sum(matching_transitions.values())
                    return {num: prob/total for num, prob in matching_transitions.items()}
        
        # 最后的后备方案：均匀分布
        return {i: 1/49 for i in range(1, 50)}


class ModelBacktestFramework:
    """模型回测框架"""
    
    def __init__(self):
        self.backtest_results = []
        self.accuracy_metrics = {}
    
    def run_backtest(self, model, historical_data: List[int], test_periods: int = 50) -> Dict[str, Any]:
        """运行模型回测"""
        if len(historical_data) < test_periods + 20:
            logger.warning("历史数据不足以进行回测")
            return {'error': '历史数据不足'}
        
        results = []
        
        for i in range(len(historical_data) - test_periods, len(historical_data)):
            try:
                # 使用历史数据训练模型
                train_data = historical_data[:i]
                actual_number = historical_data[i]
                
                # 生成预测
                if hasattr(model, 'update_historical_data'):
                    model.update_historical_data(train_data)
                
                if hasattr(model, 'calculate_number_probability'):
                    # 贝叶斯模型
                    prediction_result = model.calculate_number_probability(actual_number)
                    predicted_probs = {j: model.calculate_number_probability(j)['probability'] 
                                     for j in range(1, 50)}
                    top_prediction = max(predicted_probs.items(), key=lambda x: x[1])[0]
                    probability = prediction_result['probability']
                    confidence_interval = prediction_result['confidence_interval']
                elif hasattr(model, 'predict_next_number'):
                    # 马尔可夫链模型
                    predicted_probs = model.predict_next_number(train_data)
                    top_prediction = max(predicted_probs.items(), key=lambda x: x[1])[0]
                    probability = predicted_probs.get(actual_number, 0)
                    confidence_interval = (0, 1)  # 简化处理
                else:
                    continue
                
                # 记录结果
                result = {
                    'period': i,
                    'actual': actual_number,
                    'predicted': top_prediction,
                    'probability': probability,
                    'confidence_interval': confidence_interval,
                    'hit': actual_number == top_prediction,
                    'top_5_hit': actual_number in sorted(predicted_probs.items(), 
                                                       key=lambda x: x[1], reverse=True)[:5]
                }
                results.append(result)
                
            except Exception as e:
                logger.error(f"回测期 {i} 出错: {str(e)}")
                continue
        
        return self._calculate_metrics(results)
    
    def _calculate_metrics(self, results: List[Dict]) -> Dict[str, Any]:
        """计算回测指标"""
        if not results:
            return {'error': '无有效回测结果'}
        
        total_predictions = len(results)
        correct_predictions = sum(1 for r in results if r['hit'])
        top_5_hits = sum(1 for r in results if r['top_5_hit'])
        
        probabilities = [r['probability'] for r in results if 'probability' in r]
        avg_confidence = np.mean(probabilities) if probabilities else 0
        
        return {
            'total_predictions': total_predictions,
            'correct_predictions': correct_predictions,
            'top_5_hits': top_5_hits,
            'accuracy': correct_predictions / total_predictions,
            'top_5_accuracy': top_5_hits / total_predictions,
            'average_confidence': avg_confidence,
            'confidence_calibration': self._calculate_calibration(results),
            'detailed_results': results[-10:]  # 返回最近10次的详细结果
        }
    
    def _calculate_calibration(self, results: List[Dict]) -> Dict[str, float]:
        """计算置信度校准"""
        # 简化的校准计算
        high_conf_results = [r for r in results if r.get('probability', 0) > 0.7]
        medium_conf_results = [r for r in results if 0.3 <= r.get('probability', 0) <= 0.7]
        low_conf_results = [r for r in results if r.get('probability', 0) < 0.3]
        
        calibration = {}
        
        for conf_level, conf_results in [
            ('high', high_conf_results),
            ('medium', medium_conf_results), 
            ('low', low_conf_results)
        ]:
            if conf_results:
                accuracy = sum(1 for r in conf_results if r['hit']) / len(conf_results)
                calibration[f'{conf_level}_confidence_accuracy'] = accuracy
            else:
                calibration[f'{conf_level}_confidence_accuracy'] = 0
        
        return calibration


class EnhancedAnalysisService:
    """增强分析服务主类"""
    
    def __init__(self):
        self.bayesian_model = BayesianPredictionModel()
        self.markov_analyzer = MarkovChainAnalyzer()
        self.backtest_framework = ModelBacktestFramework()
        
    def analyze_with_enhanced_models(self, historical_data: List[int]) -> Dict[str, Any]:
        """使用增强模型进行分析"""
        try:
            # 更新模型数据
            self.bayesian_model.update_historical_data(historical_data)
            self.markov_analyzer.build_transition_matrix(historical_data)
            
            # 生成概率预测
            bayesian_predictions = {}
            for number in range(1, 50):
                bayesian_predictions[number] = self.bayesian_model.calculate_number_probability(number)
            
            # 马尔可夫链预测
            markov_predictions = self.markov_analyzer.predict_next_number(historical_data)
            
            # 模型回测
            bayesian_backtest = self.backtest_framework.run_backtest(
                self.bayesian_model, historical_data, min(50, len(historical_data) // 4)
            )
            
            markov_backtest = self.backtest_framework.run_backtest(
                self.markov_analyzer, historical_data, min(50, len(historical_data) // 4)
            )
            
            return {
                'bayesian_predictions': bayesian_predictions,
                'markov_predictions': markov_predictions,
                'model_performance': {
                    'bayesian_backtest': bayesian_backtest,
                    'markov_backtest': markov_backtest
                },
                'ensemble_prediction': self._create_ensemble_prediction(
                    bayesian_predictions, markov_predictions
                )
            }
            
        except Exception as e:
            logger.error(f"增强分析出错: {str(e)}")
            return {'error': str(e)}
    
    def _create_ensemble_prediction(self, bayesian_pred: Dict, markov_pred: Dict) -> Dict[str, Any]:
        """创建集成预测"""
        ensemble_scores = {}
        
        # 权重设置
        bayesian_weight = 0.6
        markov_weight = 0.4
        
        for number in range(1, 50):
            bayesian_prob = bayesian_pred.get(number, {}).get('probability', 1/49)
            markov_prob = markov_pred.get(number, 1/49)
            
            ensemble_score = bayesian_weight * bayesian_prob + markov_weight * markov_prob
            ensemble_scores[number] = ensemble_score
        
        # 排序获取推荐
        sorted_predictions = sorted(ensemble_scores.items(), key=lambda x: x[1], reverse=True)
        
        return {
            'top_10_predictions': sorted_predictions[:10],
            'ensemble_scores': ensemble_scores,
            'recommendation_strategy': self._generate_recommendation_strategy(sorted_predictions)
        }
    
    def _generate_recommendation_strategy(self, sorted_predictions: List[Tuple[int, float]]) -> Dict[str, Any]:
        """生成推荐策略"""
        top_numbers = [num for num, _ in sorted_predictions[:10]]
        
        return {
            'high_confidence': top_numbers[:3],
            'medium_confidence': top_numbers[3:7],
            'low_confidence': top_numbers[7:10],
            'investment_strategy': {
                'conservative': top_numbers[:2],
                'balanced': top_numbers[:5],
                'aggressive': top_numbers[:10]
            }
        }
