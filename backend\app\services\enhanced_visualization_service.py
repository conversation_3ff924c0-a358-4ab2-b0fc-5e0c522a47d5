"""
增强可视化服务 - 实现热力图、时间序列图、关联网络图和交互式筛选
"""

import numpy as np
import pandas as pd
from typing import Dict, List, Tuple, Any, Optional
from datetime import datetime, timedelta
import json
import logging
from sqlalchemy.orm import Session
from app.models.draw import Draw

logger = logging.getLogger(__name__)


class EnhancedHeatmapGenerator:
    """增强热力图生成器"""

    def __init__(self):
        self.color_scales = {
            'heat': ['#313695', '#4575b4', '#74add1', '#abd9e9',
                     '#e0f3f8', '#ffffcc', '#fee090', '#fdae61',
                     '#f46d43', '#d73027', '#a50026'],
            'frequency': ['#f7fbff', '#deebf7', '#c6dbef', '#9ecae1',
                          '#6baed6', '#4292c6', '#2171b5', '#08519c', '#08306b'],
            'missing': ['#fff5f0', '#fee0d2', '#fcbba1', '#fc9272',
                        '#fb6a4a', '#ef3b2c', '#cb181d', '#a50f15', '#67000d']
        }

    def generate_number_heatmap(self, statistics_data: Dict, heatmap_type: str = 'frequency') -> Dict:
        """生成号码热度分布热力图"""
        try:
            # 创建7x7网格布局
            heatmap_data = []
            max_value = 0
            min_value = float('inf')

            # 确定数值范围
            for number in range(1, 50):
                value = self._get_heatmap_value(
                    statistics_data, number, heatmap_type)
                max_value = max(max_value, value)
                min_value = min(min_value, value)

            # 生成网格数据
            for i in range(7):
                row = []
                for j in range(7):
                    number = i * 7 + j + 1
                    if number <= 49:
                        value = self._get_heatmap_value(
                            statistics_data, number, heatmap_type)
                        heat_level = self._calculate_heat_level(
                            value, min_value, max_value)

                        row.append({
                            'number': number,
                            'value': value,
                            'heat_level': heat_level,
                            'x': j,
                            'y': i,
                            'color_intensity': (value - min_value) / (max_value - min_value) if max_value > min_value else 0.5
                        })
                    else:
                        row.append(None)
                heatmap_data.append(row)

            return {
                'type': 'heatmap',
                'data': heatmap_data,
                'config': {
                    'colorScale': self.color_scales.get(heatmap_type, self.color_scales['heat']),
                    'valueRange': {'min': min_value, 'max': max_value},
                    'tooltip': {
                        'formatter': f'号码: {{number}}<br/>{self._get_value_label(heatmap_type)}: {{value}}<br/>热度等级: {{heat_level}}'
                    },
                    'title': self._get_heatmap_title(heatmap_type),
                    'legend': {
                        'show': True,
                        'orient': 'horizontal',
                        'left': 'center',
                        'bottom': '5%'
                    }
                },
                'statistics': {
                    'total_numbers': 49,
                    'max_value': max_value,
                    'min_value': min_value,
                    'average_value': np.mean([self._get_heatmap_value(statistics_data, i, heatmap_type) for i in range(1, 50)])
                }
            }

        except Exception as e:
            logger.error(f"生成热力图失败: {str(e)}")
            return {'error': str(e)}

    def _get_heatmap_value(self, statistics_data: Dict, number: int, heatmap_type: str) -> float:
        """获取热力图数值"""
        number_str = str(number)
        if number_str not in statistics_data:
            return 0

        data = statistics_data[number_str]

        if heatmap_type == 'frequency':
            return data.get('count', 0)
        elif heatmap_type == 'missing':
            return data.get('missing_periods', 0)
        elif heatmap_type == 'heat':
            return data.get('heat_index', 0)
        else:
            return data.get('count', 0)

    def _calculate_heat_level(self, value: float, min_val: float, max_val: float) -> str:
        """计算热度等级"""
        if max_val == min_val:
            return '中等'

        normalized = (value - min_val) / (max_val - min_val)

        if normalized >= 0.8:
            return '极热'
        elif normalized >= 0.6:
            return '很热'
        elif normalized >= 0.4:
            return '热门'
        elif normalized >= 0.2:
            return '温和'
        else:
            return '冷门'

    def _get_value_label(self, heatmap_type: str) -> str:
        """获取数值标签"""
        labels = {
            'frequency': '出现次数',
            'missing': '遗漏期数',
            'heat': '热度指数'
        }
        return labels.get(heatmap_type, '数值')

    def _get_heatmap_title(self, heatmap_type: str) -> str:
        """获取热力图标题"""
        titles = {
            'frequency': '号码频率分布热力图',
            'missing': '号码遗漏分布热力图',
            'heat': '号码热度分布热力图'
        }
        return titles.get(heatmap_type, '号码分布热力图')


class TimeSeriesVisualizer:
    """时间序列可视化器"""

    def __init__(self):
        self.chart_types = ['scatter', 'line', 'bar']
        self.time_formats = ['YYYY-MM-DD', 'YYYY-MM', 'YYYY']

    def generate_number_timeline(self, number: int, historical_data: List[Dict],
                                 chart_type: str = 'scatter') -> Dict:
        """生成号码出现时间序列图"""
        try:
            timeline_data = []

            for record in historical_data:
                if 'numbers' in record and number in record['numbers']:
                    position = record['numbers'].index(number) + 1
                    is_special = (position == 7)  # 第7位是特码

                    timeline_data.append({
                        'date': record.get('draw_time', ''),
                        'period': record.get('expect', ''),
                        'position': position,
                        'is_special': is_special,
                        'number': number,
                        'timestamp': self._parse_timestamp(record.get('draw_time', ''))
                    })

            # 按时间排序
            timeline_data.sort(key=lambda x: x['timestamp'])

            return {
                'type': 'timeline',
                'data': timeline_data,
                'config': self._get_timeline_config(chart_type, number),
                'statistics': self._calculate_timeline_statistics(timeline_data),
                'insights': self._generate_timeline_insights(timeline_data)
            }

        except Exception as e:
            logger.error(f"生成时间序列图失败: {str(e)}")
            return {'error': str(e)}

    def generate_frequency_trend(self, historical_data: List[Dict],
                                 time_window: str = 'month') -> Dict:
        """生成频率趋势图"""
        try:
            # 按时间窗口聚合数据
            aggregated_data = self._aggregate_by_time_window(
                historical_data, time_window)

            # 计算每个时间段的频率
            trend_data = []
            for time_period, records in aggregated_data.items():
                number_counts = {}
                for record in records:
                    if 'numbers' in record:
                        for num in record['numbers']:
                            number_counts[num] = number_counts.get(num, 0) + 1

                trend_data.append({
                    'time_period': time_period,
                    'total_draws': len(records),
                    'number_frequencies': number_counts,
                    'most_frequent': max(number_counts.items(), key=lambda x: x[1]) if number_counts else (0, 0),
                    'average_frequency': np.mean(list(number_counts.values())) if number_counts else 0
                })

            return {
                'type': 'frequency_trend',
                'data': trend_data,
                'config': self._get_trend_config(time_window),
                'statistics': self._calculate_trend_statistics(trend_data)
            }

        except Exception as e:
            logger.error(f"生成频率趋势图失败: {str(e)}")
            return {'error': str(e)}

    def _parse_timestamp(self, date_str: str) -> int:
        """解析时间戳"""
        try:
            if date_str:
                dt = datetime.strptime(date_str, '%Y-%m-%d')
                return int(dt.timestamp())
            return 0
        except:
            return 0

    def _get_timeline_config(self, chart_type: str, number: int) -> Dict:
        """获取时间序列图配置"""
        base_config = {
            'title': {
                'text': f'号码 {number} 出现时间序列',
                'left': 'center'
            },
            'tooltip': {
                'trigger': 'item',
                'formatter': '期号: {c.period}<br/>日期: {c.date}<br/>位置: {c.position}<br/>类型: {c.is_special}'
            },
            'xAxis': {
                'type': 'time',
                'name': '时间'
            },
            'yAxis': {
                'type': 'value',
                'name': '位置',
                'min': 1,
                'max': 7
            },
            'dataZoom': [
                {
                    'type': 'slider',
                    'start': 0,
                    'end': 100
                }
            ]
        }

        if chart_type == 'scatter':
            base_config['series'] = [{
                'type': 'scatter',
                'symbolSize': lambda params: 15 if params['is_special'] else 8,
                'itemStyle': {
                    'color': lambda params: '#ff4757' if params['is_special'] else '#3742fa'
                }
            }]
        elif chart_type == 'line':
            base_config['series'] = [{
                'type': 'line',
                'smooth': True,
                'lineStyle': {'width': 2}
            }]

        return base_config

    def _aggregate_by_time_window(self, historical_data: List[Dict], time_window: str) -> Dict:
        """按时间窗口聚合数据"""
        aggregated = {}

        for record in historical_data:
            date_str = record.get('draw_time', '')
            if not date_str:
                continue

            try:
                dt = datetime.strptime(date_str, '%Y-%m-%d')

                if time_window == 'month':
                    key = dt.strftime('%Y-%m')
                elif time_window == 'quarter':
                    quarter = (dt.month - 1) // 3 + 1
                    key = f"{dt.year}-Q{quarter}"
                elif time_window == 'year':
                    key = str(dt.year)
                else:
                    key = date_str

                if key not in aggregated:
                    aggregated[key] = []
                aggregated[key].append(record)

            except:
                continue

        return aggregated

    def _calculate_timeline_statistics(self, timeline_data: List[Dict]) -> Dict:
        """计算时间序列统计"""
        if not timeline_data:
            return {}

        positions = [item['position'] for item in timeline_data]
        special_count = sum(1 for item in timeline_data if item['is_special'])

        # 计算间隔
        intervals = []
        timestamps = [item['timestamp'] for item in timeline_data]
        for i in range(1, len(timestamps)):
            interval_days = (timestamps[i] - timestamps[i-1]) / (24 * 3600)
            intervals.append(interval_days)

        return {
            'total_appearances': len(timeline_data),
            'special_appearances': special_count,
            'normal_appearances': len(timeline_data) - special_count,
            'average_position': np.mean(positions),
            'most_common_position': max(set(positions), key=positions.count),
            'average_interval_days': np.mean(intervals) if intervals else 0,
            'min_interval_days': min(intervals) if intervals else 0,
            'max_interval_days': max(intervals) if intervals else 0
        }

    def _generate_timeline_insights(self, timeline_data: List[Dict]) -> List[str]:
        """生成时间序列洞察"""
        insights = []

        if not timeline_data:
            return ['暂无数据']

        # 分析出现频率
        if len(timeline_data) > 10:
            insights.append(f"该号码共出现 {len(timeline_data)} 次，属于活跃号码")
        elif len(timeline_data) > 5:
            insights.append(f"该号码共出现 {len(timeline_data)} 次，属于中等活跃号码")
        else:
            insights.append(f"该号码共出现 {len(timeline_data)} 次，属于冷门号码")

        # 分析特码比例
        special_count = sum(1 for item in timeline_data if item['is_special'])
        special_ratio = special_count / len(timeline_data)
        if special_ratio > 0.3:
            insights.append(f"该号码作为特码的比例较高 ({special_ratio:.1%})")
        elif special_ratio > 0.1:
            insights.append(f"该号码作为特码的比例正常 ({special_ratio:.1%})")
        else:
            insights.append(f"该号码很少作为特码出现 ({special_ratio:.1%})")

        return insights

    def _get_trend_config(self, time_window: str) -> Dict:
        """获取趋势图配置"""
        return {
            'title': {
                'text': f'号码频率趋势 (按{time_window})',
                'left': 'center'
            },
            'tooltip': {
                'trigger': 'axis'
            },
            'xAxis': {
                'type': 'category',
                'name': '时间'
            },
            'yAxis': {
                'type': 'value',
                'name': '平均频率'
            },
            'series': [{
                'type': 'line',
                'smooth': True,
                'areaStyle': {}
            }]
        }

    def _calculate_trend_statistics(self, trend_data: List[Dict]) -> Dict:
        """计算趋势统计"""
        if not trend_data:
            return {}

        frequencies = [item['average_frequency'] for item in trend_data]

        return {
            'total_periods': len(trend_data),
            'average_frequency': np.mean(frequencies),
            'max_frequency': max(frequencies),
            'min_frequency': min(frequencies),
            'frequency_trend': 'increasing' if frequencies[-1] > frequencies[0] else 'decreasing',
            'volatility': np.std(frequencies)
        }


class NetworkGraphGenerator:
    """网络图生成器"""

    def __init__(self):
        self.node_categories = {
            'hot': {'color': '#ff4757', 'name': '热门号码'},
            'warm': {'color': '#ffa502', 'name': '温和号码'},
            'cold': {'color': '#3742fa', 'name': '冷门号码'},
            'special': {'color': '#2ed573', 'name': '特码偏好'}
        }

    def generate_correlation_network(self, correlation_matrix: np.ndarray,
                                     statistics_data: Dict, threshold: float = 0.3) -> Dict:
        """生成号码关联网络图"""
        try:
            nodes = []
            links = []

            # 创建节点
            for i in range(1, 50):
                number_str = str(i)
                frequency = statistics_data.get(number_str, {}).get('count', 0)
                category = self._get_number_category(
                    frequency, statistics_data)

                nodes.append({
                    'id': str(i),
                    'name': f'号码{i}',
                    'value': frequency,
                    'category': category,
                    'symbolSize': max(10, min(30, frequency * 2)),
                    'itemStyle': {
                        'color': self.node_categories[category]['color']
                    },
                    'label': {
                        'show': frequency > 10,  # 只显示高频号码的标签
                        'fontSize': 12
                    }
                })

            # 创建连接（只显示强关联）
            for i in range(49):
                for j in range(i+1, 49):
                    if i < len(correlation_matrix) and j < len(correlation_matrix[0]):
                        correlation = correlation_matrix[i][j]
                        if abs(correlation) > threshold:
                            links.append({
                                'source': str(i+1),
                                'target': str(j+1),
                                'value': abs(correlation),
                                'lineStyle': {
                                    'width': abs(correlation) * 8,
                                    'color': '#ff6b6b' if correlation > 0 else '#4ecdc4',
                                    'opacity': min(1, abs(correlation) * 2)
                                },
                                'label': {
                                    'show': abs(correlation) > 0.5,
                                    'formatter': f'{correlation:.2f}'
                                }
                            })

            return {
                'type': 'network',
                'nodes': nodes,
                'links': links,
                'categories': [
                    {'name': cat_data['name'], 'itemStyle': {
                        'color': cat_data['color']}}
                    for cat_data in self.node_categories.values()
                ],
                'config': {
                    'layout': 'force',
                    'force': {
                        'repulsion': 1000,
                        'edgeLength': 100,
                        'gravity': 0.1
                    },
                    'roam': True,
                    'focusNodeAdjacency': True,
                    'lineStyle': {
                        'curveness': 0.3
                    }
                },
                'statistics': {
                    'total_nodes': len(nodes),
                    'total_links': len(links),
                    'average_correlation': np.mean([link['value'] for link in links]) if links else 0,
                    'max_correlation': max([link['value'] for link in links]) if links else 0
                }
            }

        except Exception as e:
            logger.error(f"生成关联网络图失败: {str(e)}")
            return {'error': str(e)}

    def generate_zodiac_network(self, historical_data: List[Dict]) -> Dict:
        """生成生肖关联网络图"""
        try:
            # 生肖映射
            zodiac_map = {
                1: '鼠', 2: '牛', 3: '虎', 4: '兔', 5: '龙', 6: '蛇',
                7: '马', 8: '羊', 9: '猴', 10: '鸡', 11: '狗', 12: '猪'
            }

            # 统计生肖共现
            zodiac_cooccurrence = {}
            zodiac_counts = {zodiac: 0 for zodiac in zodiac_map.values()}

            for record in historical_data:
                if 'numbers' in record:
                    record_zodiacs = []
                    for number in record['numbers']:
                        zodiac = zodiac_map.get(number % 12, '未知')
                        record_zodiacs.append(zodiac)
                        zodiac_counts[zodiac] += 1

                    # 计算生肖间的共现
                    for i, zodiac1 in enumerate(record_zodiacs):
                        for j, zodiac2 in enumerate(record_zodiacs):
                            if i != j:
                                key = tuple(sorted([zodiac1, zodiac2]))
                                zodiac_cooccurrence[key] = zodiac_cooccurrence.get(
                                    key, 0) + 1

            # 创建节点
            nodes = []
            for zodiac, count in zodiac_counts.items():
                nodes.append({
                    'id': zodiac,
                    'name': zodiac,
                    'value': count,
                    'symbolSize': max(20, min(60, count / 5)),
                    'category': 'zodiac'
                })

            # 创建连接
            links = []
            max_cooccurrence = max(
                zodiac_cooccurrence.values()) if zodiac_cooccurrence else 1

            for (zodiac1, zodiac2), count in zodiac_cooccurrence.items():
                if count > max_cooccurrence * 0.1:  # 只显示有意义的关联
                    links.append({
                        'source': zodiac1,
                        'target': zodiac2,
                        'value': count,
                        'lineStyle': {
                            'width': (count / max_cooccurrence) * 10,
                            'opacity': 0.6
                        }
                    })

            return {
                'type': 'zodiac_network',
                'nodes': nodes,
                'links': links,
                'config': {
                    'layout': 'circular',
                    'roam': True,
                    'label': {
                        'show': True,
                        'position': 'inside'
                    }
                },
                'statistics': {
                    'zodiac_counts': zodiac_counts,
                    'total_cooccurrences': len(zodiac_cooccurrence),
                    'strongest_association': max(zodiac_cooccurrence.items(), key=lambda x: x[1]) if zodiac_cooccurrence else None
                }
            }

        except Exception as e:
            logger.error(f"生成生肖网络图失败: {str(e)}")
            return {'error': str(e)}

    def _get_number_category(self, frequency: int, statistics_data: Dict) -> str:
        """获取号码类别"""
        all_frequencies = [data.get('count', 0)
                           for data in statistics_data.values()]
        if not all_frequencies:
            return 'cold'

        avg_frequency = np.mean(all_frequencies)
        std_frequency = np.std(all_frequencies)

        if frequency > avg_frequency + std_frequency:
            return 'hot'
        elif frequency > avg_frequency - std_frequency:
            return 'warm'
        else:
            return 'cold'


class InteractiveFilterController:
    """交互式筛选控制器"""

    def __init__(self):
        self.filter_definitions = self._initialize_filter_definitions()

    def _initialize_filter_definitions(self) -> Dict:
        """初始化筛选定义"""
        return {
            'frequency_range': {
                'type': 'slider',
                'label': '出现频率范围',
                'min': 0,
                'max': 100,
                'default': [0, 100],
                'step': 1,
                'unit': '次'
            },
            'missing_periods': {
                'type': 'slider',
                'label': '遗漏期数范围',
                'min': 0,
                'max': 200,
                'default': [0, 200],
                'step': 1,
                'unit': '期'
            },
            'zodiac_filter': {
                'type': 'checkbox',
                'label': '生肖筛选',
                'options': ['鼠', '牛', '虎', '兔', '龙', '蛇', '马', '羊', '猴', '鸡', '狗', '猪'],
                'default': []
            },
            'color_filter': {
                'type': 'checkbox',
                'label': '波色筛选',
                'options': ['红波', '蓝波', '绿波'],
                'default': []
            },
            'number_type': {
                'type': 'checkbox',
                'label': '号码类型',
                'options': ['质数', '合数', '大号(≥25)', '小号(<25)', '单数', '双数'],
                'default': []
            },
            'position_filter': {
                'type': 'checkbox',
                'label': '位置偏好',
                'options': ['第1位', '第2位', '第3位', '第4位', '第5位', '第6位', '特码位'],
                'default': []
            },
            'heat_level': {
                'type': 'select',
                'label': '热度等级',
                'options': ['全部', '极热', '很热', '热门', '温和', '冷门'],
                'default': '全部'
            },
            'time_range': {
                'type': 'daterange',
                'label': '时间范围',
                'default': None
            }
        }

    def create_filter_controls(self) -> Dict:
        """创建筛选控件配置"""
        return {
            'filters': self.filter_definitions,
            'layout': {
                'columns': 3,
                'responsive': True
            },
            'actions': {
                'apply': '应用筛选',
                'reset': '重置筛选',
                'save': '保存方案',
                'load': '加载方案'
            }
        }

    def apply_filters(self, data: List[Dict], filters: Dict) -> Dict:
        """应用筛选条件"""
        try:
            filtered_data = data.copy()
            filter_summary = []

            # 应用频率筛选
            if 'frequency_range' in filters:
                min_freq, max_freq = filters['frequency_range']
                original_count = len(filtered_data)
                filtered_data = [
                    item for item in filtered_data
                    if min_freq <= item.get('frequency', 0) <= max_freq
                ]
                filter_summary.append(
                    f"频率范围 {min_freq}-{max_freq}: {original_count} → {len(filtered_data)}")

            # 应用遗漏期数筛选
            if 'missing_periods' in filters:
                min_missing, max_missing = filters['missing_periods']
                original_count = len(filtered_data)
                filtered_data = [
                    item for item in filtered_data
                    if min_missing <= item.get('missing_periods', 0) <= max_missing
                ]
                filter_summary.append(
                    f"遗漏期数 {min_missing}-{max_missing}: {original_count} → {len(filtered_data)}")

            # 应用生肖筛选
            if filters.get('zodiac_filter'):
                selected_zodiacs = filters['zodiac_filter']
                original_count = len(filtered_data)
                filtered_data = [
                    item for item in filtered_data
                    if item.get('zodiac') in selected_zodiacs
                ]
                filter_summary.append(
                    f"生肖筛选 {selected_zodiacs}: {original_count} → {len(filtered_data)}")

            # 应用波色筛选
            if filters.get('color_filter'):
                selected_colors = filters['color_filter']
                original_count = len(filtered_data)
                filtered_data = [
                    item for item in filtered_data
                    if item.get('color') in selected_colors
                ]
                filter_summary.append(
                    f"波色筛选 {selected_colors}: {original_count} → {len(filtered_data)}")

            # 应用号码类型筛选
            if filters.get('number_type'):
                selected_types = filters['number_type']
                original_count = len(filtered_data)
                filtered_data = self._apply_number_type_filter(
                    filtered_data, selected_types)
                filter_summary.append(
                    f"号码类型 {selected_types}: {original_count} → {len(filtered_data)}")

            # 应用热度等级筛选
            if filters.get('heat_level') and filters['heat_level'] != '全部':
                heat_level = filters['heat_level']
                original_count = len(filtered_data)
                filtered_data = [
                    item for item in filtered_data
                    if item.get('heat_level') == heat_level
                ]
                filter_summary.append(
                    f"热度等级 {heat_level}: {original_count} → {len(filtered_data)}")

            return {
                'filtered_data': filtered_data,
                'filter_summary': filter_summary,
                'statistics': {
                    'original_count': len(data),
                    'filtered_count': len(filtered_data),
                    'filter_ratio': len(filtered_data) / len(data) if data else 0
                },
                'recommendations': self._generate_filter_recommendations(filtered_data)
            }

        except Exception as e:
            logger.error(f"应用筛选失败: {str(e)}")
            return {'error': str(e)}

    def _apply_number_type_filter(self, data: List[Dict], selected_types: List[str]) -> List[Dict]:
        """应用号码类型筛选"""
        filtered = []

        for item in data:
            number = item.get('number', 0)
            if not number:
                continue

            include = True

            for type_filter in selected_types:
                if type_filter == '质数' and not self._is_prime(number):
                    include = False
                    break
                elif type_filter == '合数' and self._is_prime(number):
                    include = False
                    break
                elif type_filter == '大号(≥25)' and number < 25:
                    include = False
                    break
                elif type_filter == '小号(<25)' and number >= 25:
                    include = False
                    break
                elif type_filter == '单数' and number % 2 == 0:
                    include = False
                    break
                elif type_filter == '双数' and number % 2 == 1:
                    include = False
                    break

            if include:
                filtered.append(item)

        return filtered

    def _is_prime(self, n: int) -> bool:
        """判断是否为质数"""
        if n < 2:
            return False
        for i in range(2, int(n ** 0.5) + 1):
            if n % i == 0:
                return False
        return True

    def _generate_filter_recommendations(self, filtered_data: List[Dict]) -> List[str]:
        """生成筛选建议"""
        recommendations = []

        if not filtered_data:
            recommendations.append("当前筛选条件过于严格，建议放宽筛选范围")
            return recommendations

        if len(filtered_data) > 30:
            recommendations.append("筛选结果较多，建议进一步细化筛选条件")
        elif len(filtered_data) < 5:
            recommendations.append("筛选结果较少，建议适当放宽筛选条件")
        else:
            recommendations.append("筛选结果数量适中，可以进行深入分析")

        # 分析筛选结果的特征
        if filtered_data:
            avg_frequency = np.mean([item.get('frequency', 0)
                                    for item in filtered_data])
            if avg_frequency > 15:
                recommendations.append("筛选结果中包含较多热门号码，适合稳健投注")
            elif avg_frequency < 5:
                recommendations.append("筛选结果中包含较多冷门号码，适合回补投注")

        return recommendations


class EnhancedVisualizationService:
    """增强可视化服务主类"""

    def __init__(self):
        self.heatmap_generator = EnhancedHeatmapGenerator()
        self.timeline_visualizer = TimeSeriesVisualizer()
        self.network_generator = NetworkGraphGenerator()
        self.filter_controller = InteractiveFilterController()

    def generate_comprehensive_visualization(self, statistics_data: Dict,
                                             historical_data: List[Dict],
                                             correlation_matrix: Optional[np.ndarray] = None) -> Dict:
        """生成综合可视化分析"""
        try:
            result = {
                'heatmaps': {},
                'timelines': {},
                'networks': {},
                'filters': {},
                'summary': {}
            }

            # 生成热力图
            result['heatmaps'] = {
                'frequency': self.heatmap_generator.generate_number_heatmap(statistics_data, 'frequency'),
                'missing': self.heatmap_generator.generate_number_heatmap(statistics_data, 'missing'),
                'heat': self.heatmap_generator.generate_number_heatmap(statistics_data, 'heat')
            }

            # 生成时间序列图
            result['timelines'] = {
                'frequency_trend': self.timeline_visualizer.generate_frequency_trend(historical_data, 'month'),
                'quarterly_trend': self.timeline_visualizer.generate_frequency_trend(historical_data, 'quarter')
            }

            # 生成网络图
            if correlation_matrix is not None:
                result['networks']['correlation'] = self.network_generator.generate_correlation_network(
                    correlation_matrix, statistics_data
                )

            result['networks']['zodiac'] = self.network_generator.generate_zodiac_network(
                historical_data)

            # 生成筛选控件
            result['filters'] = self.filter_controller.create_filter_controls()

            # 生成分析摘要
            result['summary'] = self._generate_analysis_summary(result)

            return result

        except Exception as e:
            logger.error(f"生成综合可视化失败: {str(e)}")
            return {'error': str(e)}

    def generate_number_specific_visualization(self, number: int, statistics_data: Dict,
                                               historical_data: List[Dict]) -> Dict:
        """生成特定号码的可视化分析"""
        try:
            # 生成该号码的时间序列
            timeline = self.timeline_visualizer.generate_number_timeline(
                number, historical_data)

            # 获取该号码的统计信息
            number_stats = statistics_data.get(str(number), {})

            # 生成相关号码网络（基于共现频率）
            related_numbers = self._find_related_numbers(
                number, historical_data)

            return {
                'number': number,
                'timeline': timeline,
                'statistics': number_stats,
                'related_numbers': related_numbers,
                'insights': self._generate_number_insights(number, number_stats, timeline)
            }

        except Exception as e:
            logger.error(f"生成号码 {number} 可视化失败: {str(e)}")
            return {'error': str(e)}

    def apply_interactive_filters(self, data: List[Dict], filters: Dict) -> Dict:
        """应用交互式筛选"""
        return self.filter_controller.apply_filters(data, filters)

    def _generate_analysis_summary(self, visualization_result: Dict) -> Dict:
        """生成分析摘要"""
        summary = {
            'total_visualizations': 0,
            'key_insights': [],
            'recommendations': []
        }

        # 统计可视化数量
        for category, items in visualization_result.items():
            if isinstance(items, dict) and category != 'summary':
                summary['total_visualizations'] += len(items)

        # 生成关键洞察
        if 'heatmaps' in visualization_result:
            heatmap_data = visualization_result['heatmaps']
            if 'frequency' in heatmap_data and 'statistics' in heatmap_data['frequency']:
                freq_stats = heatmap_data['frequency']['statistics']
                summary['key_insights'].append(
                    f"号码频率分布：最高 {freq_stats.get('max_value', 0)} 次，"
                    f"最低 {freq_stats.get('min_value', 0)} 次，"
                    f"平均 {freq_stats.get('average_value', 0):.1f} 次"
                )

        if 'networks' in visualization_result:
            network_data = visualization_result['networks']
            if 'correlation' in network_data and 'statistics' in network_data['correlation']:
                corr_stats = network_data['correlation']['statistics']
                summary['key_insights'].append(
                    f"号码关联分析：发现 {corr_stats.get('total_links', 0)} 个强关联，"
                    f"最大关联度 {corr_stats.get('max_correlation', 0):.2f}"
                )

        # 生成建议
        summary['recommendations'] = [
            "建议结合热力图和时间序列图进行综合分析",
            "关注网络图中的强关联号码组合",
            "使用交互式筛选功能深入挖掘数据模式",
            "定期更新可视化分析以跟踪趋势变化"
        ]

        return summary

    def _find_related_numbers(self, target_number: int, historical_data: List[Dict]) -> List[Dict]:
        """查找相关号码"""
        cooccurrence = {}
        target_appearances = 0

        for record in historical_data:
            if 'numbers' in record and target_number in record['numbers']:
                target_appearances += 1
                for number in record['numbers']:
                    if number != target_number:
                        cooccurrence[number] = cooccurrence.get(number, 0) + 1

        # 计算关联强度
        related = []
        for number, count in cooccurrence.items():
            strength = count / target_appearances if target_appearances > 0 else 0
            if strength > 0.1:  # 只保留有意义的关联
                related.append({
                    'number': number,
                    'cooccurrence_count': count,
                    'strength': strength,
                    'relationship_type': self._classify_relationship(strength)
                })

        # 按关联强度排序
        related.sort(key=lambda x: x['strength'], reverse=True)
        return related[:10]  # 返回前10个相关号码

    def _classify_relationship(self, strength: float) -> str:
        """分类关联关系"""
        if strength > 0.5:
            return '强关联'
        elif strength > 0.3:
            return '中等关联'
        elif strength > 0.1:
            return '弱关联'
        else:
            return '无关联'

    def _generate_number_insights(self, number: int, stats: Dict, timeline: Dict) -> List[str]:
        """生成号码洞察"""
        insights = []

        # 频率分析
        frequency = stats.get('count', 0)
        if frequency > 20:
            insights.append(f"号码 {number} 是热门号码，出现频率较高")
        elif frequency > 10:
            insights.append(f"号码 {number} 是温和号码，出现频率中等")
        else:
            insights.append(f"号码 {number} 是冷门号码，出现频率较低")

        # 遗漏分析
        missing = stats.get('missing_periods', 0)
        if missing > 30:
            insights.append(f"号码 {number} 当前遗漏 {missing} 期，具有回补潜力")
        elif missing > 15:
            insights.append(f"号码 {number} 当前遗漏 {missing} 期，需要关注")
        else:
            insights.append(f"号码 {number} 当前遗漏 {missing} 期，状态正常")

        # 时间序列分析
        if 'statistics' in timeline:
            timeline_stats = timeline['statistics']
            special_ratio = timeline_stats.get(
                'special_appearances', 0) / max(1, timeline_stats.get('total_appearances', 1))
            if special_ratio > 0.2:
                insights.append(f"号码 {number} 作为特码的概率较高 ({special_ratio:.1%})")

        return insights


# 工具函数
def calculate_correlation_matrix(historical_data: List[Dict]) -> np.ndarray:
    """计算号码关联矩阵"""
    try:
        # 创建号码出现矩阵
        number_matrix = []
        for record in historical_data:
            if 'numbers' in record:
                row = [0] * 49
                for number in record['numbers']:
                    if 1 <= number <= 49:
                        row[number - 1] = 1
                number_matrix.append(row)

        if not number_matrix:
            return np.zeros((49, 49))

        # 计算相关系数矩阵
        number_matrix = np.array(number_matrix)
        correlation_matrix = np.corrcoef(number_matrix.T)

        # 处理NaN值
        correlation_matrix = np.nan_to_num(correlation_matrix, nan=0.0)

        return correlation_matrix

    except Exception as e:
        logger.error(f"计算关联矩阵失败: {str(e)}")
        return np.zeros((49, 49))


def export_visualization_data(visualization_result: Dict, format_type: str = 'json') -> str:
    """导出可视化数据"""
    try:
        if format_type == 'json':
            return json.dumps(visualization_result, ensure_ascii=False, indent=2)
        elif format_type == 'csv':
            # 简化的CSV导出，只导出统计数据
            csv_data = []
            if 'heatmaps' in visualization_result:
                for heatmap_type, heatmap_data in visualization_result['heatmaps'].items():
                    if 'data' in heatmap_data:
                        for row in heatmap_data['data']:
                            for cell in row:
                                if cell:
                                    csv_data.append([
                                        heatmap_type,
                                        cell['number'],
                                        cell['value'],
                                        cell['heat_level']
                                    ])

            import csv
            import io
            output = io.StringIO()
            writer = csv.writer(output)
            writer.writerow(['类型', '号码', '数值', '热度等级'])
            writer.writerows(csv_data)
            return output.getvalue()
        else:
            return str(visualization_result)

    except Exception as e:
        logger.error(f"导出可视化数据失败: {str(e)}")
        return f"导出失败: {str(e)}"
