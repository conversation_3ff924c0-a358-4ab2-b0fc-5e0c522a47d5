<template>
  <div class="enhanced-visualization">
    <!-- 页面标题 -->
    <div class="page-header">
      <h1>🎨 增强可视化分析</h1>
      <p>提供热力图、时间序列图、关联网络图和交互式筛选功能</p>
    </div>

    <!-- 功能选项卡 -->
    <el-tabs v-model="activeTab" @tab-change="handleTabChange">
      <!-- 热力图分析 -->
      <el-tab-pane label="🔥 热力图分析" name="heatmap">
        <div class="heatmap-section">
          <div class="controls">
            <el-select v-model="heatmapType" @change="loadHeatmap" placeholder="选择热力图类型">
              <el-option label="频率热力图" value="frequency"></el-option>
              <el-option label="遗漏热力图" value="missing"></el-option>
              <el-option label="热度热力图" value="heat"></el-option>
            </el-select>
            <el-button type="primary" @click="loadHeatmap" :loading="loading.heatmap">
              <i class="el-icon-refresh"></i> 刷新
            </el-button>
          </div>

          <div class="chart-container">
            <div ref="heatmapChart" style="width: 100%; height: 500px;"></div>
          </div>

          <div v-if="heatmapData.statistics" class="statistics-panel">
            <h3>📊 统计信息</h3>
            <el-row :gutter="20">
              <el-col :span="6">
                <el-statistic title="最大值" :value="heatmapData.statistics.max_value"></el-statistic>
              </el-col>
              <el-col :span="6">
                <el-statistic title="最小值" :value="heatmapData.statistics.min_value"></el-statistic>
              </el-col>
              <el-col :span="6">
                <el-statistic title="平均值" :value="heatmapData.statistics.average_value" :precision="1"></el-statistic>
              </el-col>
              <el-col :span="6">
                <el-statistic title="总号码数" :value="heatmapData.statistics.total_numbers"></el-statistic>
              </el-col>
            </el-row>
          </div>
        </div>
      </el-tab-pane>

      <!-- 时间序列分析 -->
      <el-tab-pane label="📈 时间序列分析" name="timeline">
        <div class="timeline-section">
          <div class="controls">
            <el-input-number
              v-model="selectedNumber"
              :min="1"
              :max="49"
              placeholder="选择号码"
              style="width: 120px; margin-right: 10px;">
            </el-input-number>
            <el-select v-model="chartType" style="width: 120px; margin-right: 10px;">
              <el-option label="散点图" value="scatter"></el-option>
              <el-option label="折线图" value="line"></el-option>
              <el-option label="柱状图" value="bar"></el-option>
            </el-select>
            <el-button type="primary" @click="loadTimeline" :loading="loading.timeline">
              <i class="el-icon-search"></i> 分析
            </el-button>
          </div>

          <div class="chart-container">
            <div ref="timelineChart" style="width: 100%; height: 500px;"></div>
          </div>

          <div v-if="timelineData.statistics" class="insights-panel">
            <h3>💡 分析洞察</h3>
            <el-row :gutter="20">
              <el-col :span="12">
                <div class="insight-card">
                  <h4>出现统计</h4>
                  <p>总出现次数: {{ timelineData.statistics.total_appearances }}</p>
                  <p>特码次数: {{ timelineData.statistics.special_appearances }}</p>
                  <p>平码次数: {{ timelineData.statistics.normal_appearances }}</p>
                </div>
              </el-col>
              <el-col :span="12">
                <div class="insight-card">
                  <h4>间隔分析</h4>
                  <p>平均间隔: {{ timelineData.statistics.average_interval_days?.toFixed(1) }} 天</p>
                  <p>最短间隔: {{ timelineData.statistics.min_interval_days }} 天</p>
                  <p>最长间隔: {{ timelineData.statistics.max_interval_days }} 天</p>
                </div>
              </el-col>
            </el-row>

            <div v-if="timelineData.insights" class="insights-list">
              <h4>🔍 智能洞察</h4>
              <ul>
                <li v-for="insight in timelineData.insights" :key="insight">{{ insight }}</li>
              </ul>
            </div>
          </div>
        </div>
      </el-tab-pane>

      <!-- 关联网络分析 -->
      <el-tab-pane label="🕸️ 关联网络分析" name="network">
        <div class="network-section">
          <div class="controls">
            <el-select v-model="networkType" @change="loadNetwork" style="width: 150px; margin-right: 10px;">
              <el-option label="号码关联网络" value="correlation"></el-option>
              <el-option label="生肖关联网络" value="zodiac"></el-option>
            </el-select>
            <el-slider
              v-if="networkType === 'correlation'"
              v-model="correlationThreshold"
              :min="0.1"
              :max="1.0"
              :step="0.1"
              :format-tooltip="formatThreshold"
              style="width: 200px; margin: 0 20px;">
            </el-slider>
            <el-button type="primary" @click="loadNetwork" :loading="loading.network">
              <i class="el-icon-connection"></i> 生成网络
            </el-button>
          </div>

          <div class="chart-container">
            <div ref="networkChart" style="width: 100%; height: 600px;"></div>
          </div>

          <div v-if="networkData.statistics" class="network-stats">
            <h3>🔗 网络统计</h3>
            <el-row :gutter="20">
              <el-col :span="8">
                <el-statistic title="节点数量" :value="networkData.statistics.total_nodes"></el-statistic>
              </el-col>
              <el-col :span="8">
                <el-statistic title="连接数量" :value="networkData.statistics.total_links"></el-statistic>
              </el-col>
              <el-col :span="8">
                <el-statistic
                  title="平均关联度"
                  :value="networkData.statistics.average_correlation"
                  :precision="3">
                </el-statistic>
              </el-col>
            </el-row>
          </div>
        </div>
      </el-tab-pane>

      <!-- 交互式筛选 -->
      <el-tab-pane label="🎛️ 交互式筛选" name="filter">
        <div class="filter-section">
          <div class="filter-controls">
            <h3>筛选条件</h3>
            <el-form :model="filterForm" label-width="120px">
              <el-row :gutter="20">
                <el-col :span="8">
                  <el-form-item label="频率范围">
                    <el-slider
                      v-model="filterForm.frequency_range"
                      range
                      :min="0"
                      :max="100"
                      :format-tooltip="formatFrequency">
                    </el-slider>
                  </el-form-item>
                </el-col>
                <el-col :span="8">
                  <el-form-item label="遗漏期数">
                    <el-slider
                      v-model="filterForm.missing_periods"
                      range
                      :min="0"
                      :max="200">
                    </el-slider>
                  </el-form-item>
                </el-col>
                <el-col :span="8">
                  <el-form-item label="热度等级">
                    <el-select v-model="filterForm.heat_level" placeholder="选择热度等级">
                      <el-option label="全部" value="全部"></el-option>
                      <el-option label="极热" value="极热"></el-option>
                      <el-option label="很热" value="很热"></el-option>
                      <el-option label="热门" value="热门"></el-option>
                      <el-option label="温和" value="温和"></el-option>
                      <el-option label="冷门" value="冷门"></el-option>
                    </el-select>
                  </el-form-item>
                </el-col>
              </el-row>

              <el-row :gutter="20">
                <el-col :span="8">
                  <el-form-item label="生肖筛选">
                    <el-select v-model="filterForm.zodiac_filter" multiple placeholder="选择生肖">
                      <el-option v-for="zodiac in zodiacOptions" :key="zodiac" :label="zodiac" :value="zodiac"></el-option>
                    </el-select>
                  </el-form-item>
                </el-col>
                <el-col :span="8">
                  <el-form-item label="波色筛选">
                    <el-select v-model="filterForm.color_filter" multiple placeholder="选择波色">
                      <el-option label="红波" value="红波"></el-option>
                      <el-option label="蓝波" value="蓝波"></el-option>
                      <el-option label="绿波" value="绿波"></el-option>
                    </el-select>
                  </el-form-item>
                </el-col>
                <el-col :span="8">
                  <el-form-item label="号码类型">
                    <el-select v-model="filterForm.number_type" multiple placeholder="选择类型">
                      <el-option label="质数" value="质数"></el-option>
                      <el-option label="合数" value="合数"></el-option>
                      <el-option label="大号(≥25)" value="大号(≥25)"></el-option>
                      <el-option label="小号(<25)" value="小号(<25)"></el-option>
                      <el-option label="单数" value="单数"></el-option>
                      <el-option label="双数" value="双数"></el-option>
                    </el-select>
                  </el-form-item>
                </el-col>
              </el-row>

              <el-form-item>
                <el-button type="primary" @click="applyFilters" :loading="loading.filter">
                  <i class="el-icon-filter"></i> 应用筛选
                </el-button>
                <el-button @click="resetFilters">
                  <i class="el-icon-refresh-left"></i> 重置
                </el-button>
              </el-form-item>
            </el-form>
          </div>

          <div v-if="filterResult.filtered_data" class="filter-results">
            <h3>筛选结果</h3>
            <div class="filter-summary">
              <el-alert
                :title="`筛选完成：${filterResult.statistics.original_count} → ${filterResult.statistics.filtered_count} 个号码`"
                type="info"
                show-icon>
              </el-alert>

              <div class="summary-list">
                <h4>筛选过程</h4>
                <ul>
                  <li v-for="summary in filterResult.filter_summary" :key="summary">{{ summary }}</li>
                </ul>
              </div>

              <div v-if="filterResult.recommendations" class="recommendations">
                <h4>💡 建议</h4>
                <ul>
                  <li v-for="rec in filterResult.recommendations" :key="rec">{{ rec }}</li>
                </ul>
              </div>
            </div>

            <div class="filtered-numbers">
              <h4>筛选结果号码</h4>
              <div class="number-grid">
                <div
                  v-for="item in filterResult.filtered_data"
                  :key="item.number"
                  class="number-card"
                  :class="getNumberCardClass(item)">
                  <div class="number">{{ item.number }}</div>
                  <div class="stats">
                    <div>频率: {{ item.frequency }}</div>
                    <div>遗漏: {{ item.missing_periods }}</div>
                    <div>{{ item.heat_level }}</div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </el-tab-pane>
    </el-tabs>
  </div>
</template>

<script>
import { ref, reactive, onMounted, nextTick } from 'vue'
import * as echarts from 'echarts'
import { ElMessage } from 'element-plus'

export default {
  name: 'EnhancedVisualization',
  setup() {
    // 响应式数据
    const activeTab = ref('heatmap')
    const loading = reactive({
      heatmap: false,
      timeline: false,
      network: false,
      filter: false
    })

    // 热力图相关
    const heatmapType = ref('frequency')
    const heatmapData = ref({})
    const heatmapChart = ref(null)
    let heatmapChartInstance = null

    // 时间序列相关
    const selectedNumber = ref(7)
    const chartType = ref('scatter')
    const timelineData = ref({})
    const timelineChart = ref(null)
    let timelineChartInstance = null

    // 网络图相关
    const networkType = ref('correlation')
    const correlationThreshold = ref(0.3)
    const networkData = ref({})
    const networkChart = ref(null)
    let networkChartInstance = null

    // 筛选相关
    const filterForm = reactive({
      frequency_range: [0, 100],
      missing_periods: [0, 200],
      heat_level: '全部',
      zodiac_filter: [],
      color_filter: [],
      number_type: []
    })
    const filterResult = ref({})
    const zodiacOptions = ['鼠', '牛', '虎', '兔', '龙', '蛇', '马', '羊', '猴', '鸡', '狗', '猪']

    // API调用函数
    const loadHeatmap = async () => {
      loading.heatmap = true
      try {
        const response = await fetch(`/api/enhanced-visualization/heatmap/${heatmapType.value}`)
        const result = await response.json()

        if (result.success) {
          heatmapData.value = result.data
          await nextTick()
          renderHeatmap()
          ElMessage.success('热力图加载成功')
        } else {
          ElMessage.error('热力图加载失败')
        }
      } catch (error) {
        console.error('加载热力图失败:', error)
        ElMessage.error('网络错误')
      } finally {
        loading.heatmap = false
      }
    }

    const loadTimeline = async () => {
      loading.timeline = true
      try {
        const response = await fetch(`/api/enhanced-visualization/timeline/${selectedNumber.value}?chart_type=${chartType.value}`)
        const result = await response.json()

        if (result.success) {
          timelineData.value = result.data
          await nextTick()
          renderTimeline()
          ElMessage.success('时间序列图加载成功')
        } else {
          ElMessage.error('时间序列图加载失败')
        }
      } catch (error) {
        console.error('加载时间序列图失败:', error)
        ElMessage.error('网络错误')
      } finally {
        loading.timeline = false
      }
    }

    const loadNetwork = async () => {
      loading.network = true
      try {
        let url = `/api/enhanced-visualization/network/${networkType.value}`
        if (networkType.value === 'correlation') {
          url += `?threshold=${correlationThreshold.value}`
        }

        const response = await fetch(url)
        const result = await response.json()

        if (result.success) {
          networkData.value = result.data
          await nextTick()
          renderNetwork()
          ElMessage.success('网络图加载成功')
        } else {
          ElMessage.error('网络图加载失败')
        }
      } catch (error) {
        console.error('加载网络图失败:', error)
        ElMessage.error('网络错误')
      } finally {
        loading.network = false
      }
    }

    const applyFilters = async () => {
      loading.filter = true
      try {
        const response = await fetch('/api/enhanced-visualization/filters/apply', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json'
          },
          body: JSON.stringify(filterForm)
        })
        const result = await response.json()

        if (result.success) {
          filterResult.value = result.data
          ElMessage.success('筛选应用成功')
        } else {
          ElMessage.error('筛选应用失败')
        }
      } catch (error) {
        console.error('应用筛选失败:', error)
        ElMessage.error('网络错误')
      } finally {
        loading.filter = false
      }
    }

    // 图表渲染函数
    const renderHeatmap = () => {
      if (!heatmapChart.value || !heatmapData.value.data) return

      if (heatmapChartInstance) {
        heatmapChartInstance.dispose()
      }

      heatmapChartInstance = echarts.init(heatmapChart.value)

      const data = []
      const maxValue = heatmapData.value.statistics?.max_value || 1

      heatmapData.value.data.forEach((row, i) => {
        row.forEach((cell, j) => {
          if (cell) {
            data.push([j, 6-i, cell.value, cell.number])
          }
        })
      })

      const option = {
        title: {
          text: heatmapData.value.config?.title || '号码热力图',
          left: 'center'
        },
        tooltip: {
          position: 'top',
          formatter: function(params) {
            return `号码: ${params.data[3]}<br/>数值: ${params.data[2]}`
          }
        },
        grid: {
          height: '60%',
          top: '10%'
        },
        xAxis: {
          type: 'category',
          data: Array.from({length: 7}, (_, i) => i + 1),
          splitArea: {
            show: true
          }
        },
        yAxis: {
          type: 'category',
          data: Array.from({length: 7}, (_, i) => 7 - i),
          splitArea: {
            show: true
          }
        },
        visualMap: {
          min: 0,
          max: maxValue,
          calculable: true,
          orient: 'horizontal',
          left: 'center',
          bottom: '5%',
          inRange: {
            color: heatmapData.value.config?.colorScale || ['#313695', '#4575b4', '#74add1', '#abd9e9', '#e0f3f8', '#ffffcc', '#fee090', '#fdae61', '#f46d43', '#d73027', '#a50026']
          }
        },
        series: [{
          name: '号码热力图',
          type: 'heatmap',
          data: data,
          label: {
            show: true,
            formatter: function(params) {
              return params.data[3] // 显示号码
            }
          },
          emphasis: {
            itemStyle: {
              shadowBlur: 10,
              shadowColor: 'rgba(0, 0, 0, 0.5)'
            }
          }
        }]
      }

      heatmapChartInstance.setOption(option)
    }

    const renderTimeline = () => {
      if (!timelineChart.value || !timelineData.value.data) return

      if (timelineChartInstance) {
        timelineChartInstance.dispose()
      }

      timelineChartInstance = echarts.init(timelineChart.value)

      const data = timelineData.value.data.map(item => [
        item.date,
        item.position,
        item.period,
        item.is_special
      ])

      const option = {
        title: {
          text: `号码 ${selectedNumber.value} 出现时间序列`,
          left: 'center'
        },
        tooltip: {
          trigger: 'item',
          formatter: function(params) {
            const [date, position, period, isSpecial] = params.data
            return `期号: ${period}<br/>日期: ${date}<br/>位置: 第${position}位<br/>类型: ${isSpecial ? '特码' : '平码'}`
          }
        },
        xAxis: {
          type: 'time',
          name: '时间'
        },
        yAxis: {
          type: 'value',
          name: '位置',
          min: 1,
          max: 7,
          interval: 1
        },
        dataZoom: [
          {
            type: 'slider',
            start: 0,
            end: 100
          }
        ],
        series: [{
          type: chartType.value,
          data: data,
          symbolSize: function(data) {
            return data[3] ? 15 : 8 // 特码用大圆点
          },
          itemStyle: {
            color: function(params) {
              return params.data[3] ? '#ff4757' : '#3742fa'
            }
          }
        }]
      }

      timelineChartInstance.setOption(option)
    }

    const renderNetwork = () => {
      if (!networkChart.value || !networkData.value.nodes) return

      if (networkChartInstance) {
        networkChartInstance.dispose()
      }

      networkChartInstance = echarts.init(networkChart.value)

      const option = {
        title: {
          text: networkType.value === 'correlation' ? '号码关联网络图' : '生肖关联网络图',
          left: 'center'
        },
        tooltip: {
          trigger: 'item',
          formatter: function(params) {
            if (params.dataType === 'node') {
              return `${params.data.name}<br/>数值: ${params.data.value}`
            } else {
              return `${params.data.source} - ${params.data.target}<br/>关联度: ${params.data.value.toFixed(3)}`
            }
          }
        },
        legend: networkData.value.categories ? {
          data: networkData.value.categories.map(cat => cat.name),
          bottom: '5%'
        } : undefined,
        series: [{
          type: 'graph',
          layout: networkData.value.config?.layout || 'force',
          data: networkData.value.nodes,
          links: networkData.value.links,
          categories: networkData.value.categories,
          roam: true,
          focusNodeAdjacency: true,
          force: networkData.value.config?.force || {
            repulsion: 1000,
            edgeLength: 100
          },
          label: {
            show: true,
            position: 'inside',
            fontSize: 10
          },
          lineStyle: {
            color: 'source',
            curveness: 0.3
          },
          emphasis: {
            focus: 'adjacency',
            lineStyle: {
              width: 10
            }
          }
        }]
      }

      networkChartInstance.setOption(option)
    }

    // 工具函数
    const handleTabChange = (tabName) => {
      activeTab.value = tabName

      // 根据选项卡加载对应数据
      nextTick(() => {
        switch (tabName) {
          case 'heatmap':
            if (!heatmapData.value.data) {
              loadHeatmap()
            }
            break
          case 'timeline':
            // 时间序列需要手动触发
            break
          case 'network':
            if (!networkData.value.nodes) {
              loadNetwork()
            }
            break
          case 'filter':
            // 筛选功能不需要自动加载
            break
        }
      })
    }

    const resetFilters = () => {
      filterForm.frequency_range = [0, 100]
      filterForm.missing_periods = [0, 200]
      filterForm.heat_level = '全部'
      filterForm.zodiac_filter = []
      filterForm.color_filter = []
      filterForm.number_type = []
      filterResult.value = {}
    }

    const formatThreshold = (value) => {
      return `${(value * 100).toFixed(0)}%`
    }

    const formatFrequency = (value) => {
      return `${value}次`
    }

    const getNumberCardClass = (item) => {
      const classes = ['number-card']

      if (item.heat_level === '极热') {
        classes.push('extremely-hot')
      } else if (item.heat_level === '很热') {
        classes.push('very-hot')
      } else if (item.heat_level === '热门') {
        classes.push('hot')
      } else if (item.heat_level === '温和') {
        classes.push('warm')
      } else {
        classes.push('cold')
      }

      return classes.join(' ')
    }

    // 生命周期
    onMounted(() => {
      // 默认加载热力图
      loadHeatmap()
    })

    return {
      // 响应式数据
      activeTab,
      loading,

      // 热力图
      heatmapType,
      heatmapData,
      heatmapChart,

      // 时间序列
      selectedNumber,
      chartType,
      timelineData,
      timelineChart,

      // 网络图
      networkType,
      correlationThreshold,
      networkData,
      networkChart,

      // 筛选
      filterForm,
      filterResult,
      zodiacOptions,

      // 方法
      handleTabChange,
      loadHeatmap,
      loadTimeline,
      loadNetwork,
      applyFilters,
      resetFilters,
      formatThreshold,
      formatFrequency,
      getNumberCardClass
    }
  }
}
</script>

<style scoped>
.enhanced-visualization {
  padding: 20px;
  background: #f5f7fa;
  min-height: 100vh;
}

.page-header {
  text-align: center;
  margin-bottom: 30px;
  padding: 20px;
  background: white;
  border-radius: 10px;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
}

.page-header h1 {
  color: #2c3e50;
  margin-bottom: 10px;
}

.page-header p {
  color: #7f8c8d;
  font-size: 16px;
}

.controls {
  display: flex;
  align-items: center;
  gap: 15px;
  margin-bottom: 20px;
  padding: 15px;
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.chart-container {
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
  margin-bottom: 20px;
  padding: 20px;
}

.statistics-panel, .insights-panel, .network-stats {
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  padding: 20px;
  margin-bottom: 20px;
}

.statistics-panel h3, .insights-panel h3, .network-stats h3 {
  color: #2c3e50;
  margin-bottom: 15px;
  border-bottom: 2px solid #3498db;
  padding-bottom: 10px;
}

.insight-card {
  background: #f8f9fa;
  border-radius: 6px;
  padding: 15px;
  border-left: 4px solid #3498db;
}

.insight-card h4 {
  color: #2c3e50;
  margin-bottom: 10px;
}

.insight-card p {
  margin: 5px 0;
  color: #5a6c7d;
}

.insights-list {
  margin-top: 20px;
}

.insights-list h4 {
  color: #2c3e50;
  margin-bottom: 10px;
}

.insights-list ul {
  list-style: none;
  padding: 0;
}

.insights-list li {
  background: #e8f4fd;
  margin: 8px 0;
  padding: 10px 15px;
  border-radius: 6px;
  border-left: 3px solid #3498db;
}

.filter-controls {
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  padding: 20px;
  margin-bottom: 20px;
}

.filter-controls h3 {
  color: #2c3e50;
  margin-bottom: 20px;
  border-bottom: 2px solid #3498db;
  padding-bottom: 10px;
}

.filter-results {
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  padding: 20px;
}

.filter-summary {
  margin-bottom: 20px;
}

.summary-list, .recommendations {
  margin-top: 15px;
}

.summary-list h4, .recommendations h4 {
  color: #2c3e50;
  margin-bottom: 10px;
}

.summary-list ul, .recommendations ul {
  list-style: none;
  padding: 0;
}

.summary-list li, .recommendations li {
  background: #f8f9fa;
  margin: 5px 0;
  padding: 8px 12px;
  border-radius: 4px;
  border-left: 3px solid #17a2b8;
}

.number-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(120px, 1fr));
  gap: 15px;
  margin-top: 15px;
}

.number-card {
  background: white;
  border-radius: 8px;
  padding: 15px;
  text-align: center;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  transition: transform 0.2s, box-shadow 0.2s;
}

.number-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.15);
}

.number-card .number {
  font-size: 24px;
  font-weight: bold;
  margin-bottom: 10px;
}

.number-card .stats {
  font-size: 12px;
  color: #7f8c8d;
}

.number-card .stats div {
  margin: 2px 0;
}

/* 热度等级颜色 */
.number-card.extremely-hot .number {
  color: #e74c3c;
}

.number-card.very-hot .number {
  color: #f39c12;
}

.number-card.hot .number {
  color: #f1c40f;
}

.number-card.warm .number {
  color: #3498db;
}

.number-card.cold .number {
  color: #95a5a6;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .enhanced-visualization {
    padding: 10px;
  }

  .controls {
    flex-direction: column;
    align-items: stretch;
  }

  .number-grid {
    grid-template-columns: repeat(auto-fill, minmax(100px, 1fr));
    gap: 10px;
  }
}
</style>
